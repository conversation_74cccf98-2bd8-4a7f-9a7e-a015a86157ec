package com.siemens.spine.logic.validator.query.visitor;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.statement.StatementVisitorAdapter;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectItem;
import net.sf.jsqlparser.statement.update.Update;

import java.util.List;

@Getter
@Slf4j
public class DangerousPatternVisitor extends StatementVisitorAdapter<Void> {

    private boolean dangerous = false;
    private String dangerReason = "";

    @Override
    public void visit(Select select) {
        checkSelect(select);
    }

    @Override
    public Void visit(Select select, Object context) {
        checkSelect(select);
        return super.visit(select, context);
    }

    @Override
    public void visit(Update update) {
        checkUpdate(update);
    }

    @Override
    public Void visit(Update update, Object context) {
        checkUpdate(update);
        return super.visit(update, context);
    }

    @Override
    public void visit(Delete delete) {
        checkDelete(delete);
    }

    @Override
    public Void visit(Delete delete, Object context) {
        checkDelete(delete);
        return super.visit(delete, context);
    }

    private void checkSelect(Select select) {
        FunctionCallVisitor functionVisitor = new FunctionCallVisitor();
        if (select.getSelectBody() != null) {
            log.debug("Checking SELECT body: {}", select.getSelectBody());
            if (select.getSelectBody() instanceof PlainSelect) {
                PlainSelect plainSelect = (PlainSelect) select.getSelectBody();

                // Check SELECT items (columns/expressions)
                List<?> selectItems = plainSelect.getSelectItems();
                if (selectItems != null) {
                    for (Object itemObj : selectItems) {
                        SelectItem item = (SelectItem) itemObj;
                        if (item.getExpression() != null) {
                            log.debug("Checking SELECT item expression: {}", item.getExpression());
                            item.getExpression().accept(functionVisitor);
                        }
                    }
                }

                // Check WHERE clause
                Expression where = plainSelect.getWhere();
                if (where != null) {
                    log.debug("Checking WHERE clause: {}", where);
                    where.accept(functionVisitor);
                }

                // Check HAVING clause
                Expression having = plainSelect.getHaving();
                if (having != null) {
                    log.debug("Checking HAVING clause: {}", having);
                    having.accept(functionVisitor);
                }

                // Check ORDER BY expressions
                if (plainSelect.getOrderByElements() != null) {
                    plainSelect.getOrderByElements().forEach(orderBy -> {
                        if (orderBy.getExpression() != null) {
                            log.debug("Checking ORDER BY expression: {}", orderBy.getExpression());
                            orderBy.getExpression().accept(functionVisitor);
                        }
                    });
                }

                // Check GROUP BY expressions
                if (plainSelect.getGroupBy() != null && plainSelect.getGroupBy().getGroupByExpressions() != null) {
                    plainSelect.getGroupBy().getGroupByExpressions().forEach(groupByObj -> {
                        Expression groupBy = (Expression) groupByObj;
                        log.debug("Checking GROUP BY expression: {}", groupBy);
                        groupBy.accept(functionVisitor);
                    });
                }
            }

            if (functionVisitor.hasDangerousFunctions()) {
                dangerous = true;
                dangerReason = "Query contains potentially dangerous functions: " + functionVisitor.getDangerousFunctions();
            }
        }
    }

    private void checkUpdate(Update update) {
        if (update.getWhere() == null) {
            dangerous = true;
            dangerReason = "UPDATE without WHERE clause is not allowed";
        }
    }

    private void checkDelete(Delete delete) {
        if (delete.getWhere() == null) {
            dangerous = true;
            dangerReason = "DELETE without WHERE clause is not allowed";
        }
    }

}