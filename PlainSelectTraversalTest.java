import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.SelectItem;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.ExpressionVisitorAdapter;
import net.sf.jsqlparser.expression.Function;
import java.util.List;

public class PlainSelectTraversalTest {
    
    static class TestExpressionVisitor extends ExpressionVisitorAdapter {
        @Override
        public void visit(Function function) {
            System.out.println("Found function: " + function.getName());
            super.visit(function);
        }
        
        @Override
        public Object visit(Function function, Object context) {
            System.out.println("Found function (Object): " + function.getName());
            return super.visit(function, context);
        }
    }
    
    public static void main(String[] args) {
        String sql = "SELECT LOAD_FILE('/etc/passwd'), id FROM users WHERE SLEEP(5) > 0";
        
        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof Select) {
                Select select = (Select) statement;
                Object selectBody = select.getSelectBody();
                
                if (selectBody instanceof PlainSelect) {
                    PlainSelect plainSelect = (PlainSelect) selectBody;
                    TestExpressionVisitor visitor = new TestExpressionVisitor();
                    
                    System.out.println("=== Traversing SELECT items ===");
                    List<?> selectItems = plainSelect.getSelectItems();
                    if (selectItems != null) {
                        for (Object itemObj : selectItems) {
                            SelectItem item = (SelectItem) itemObj;
                            System.out.println("SelectItem: " + item);
                            if (item.getExpression() != null) {
                                System.out.println("  Expression: " + item.getExpression());
                                item.getExpression().accept(visitor);
                            }
                        }
                    }
                    
                    System.out.println("\n=== Traversing WHERE clause ===");
                    Expression where = plainSelect.getWhere();
                    if (where != null) {
                        System.out.println("WHERE: " + where);
                        where.accept(visitor);
                    }
                    
                    System.out.println("\n=== Traversing HAVING clause ===");
                    Expression having = plainSelect.getHaving();
                    if (having != null) {
                        System.out.println("HAVING: " + having);
                        having.accept(visitor);
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
