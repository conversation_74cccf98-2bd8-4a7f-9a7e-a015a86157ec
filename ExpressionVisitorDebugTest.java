import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.expression.ExpressionVisitorAdapter;
import net.sf.jsqlparser.expression.Function;

public class ExpressionVisitorDebugTest {
    
    static class TestExpressionVisitor extends ExpressionVisitorAdapter {
        private boolean visited = false;
        private String visitedFunction = "";
        
        @Override
        public void visit(Function function) {
            System.out.println("Visiting Function (void): " + function.getName());
            visited = true;
            visitedFunction = function.getName();
            super.visit(function);
        }
        
        @Override
        public Object visit(Function function, Object context) {
            System.out.println("Visiting Function (Object): " + function.getName() + ", context: " + context);
            visited = true;
            visitedFunction = function.getName();
            return super.visit(function, context);
        }
        
        public boolean wasVisited() {
            return visited;
        }
        
        public String getVisitedFunction() {
            return visitedFunction;
        }
    }
    
    public static void main(String[] args) {
        String sql = "SELECT LOAD_FILE('/etc/passwd') FROM users";
        
        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof Select) {
                Select select = (Select) statement;
                Object selectBody = select.getSelectBody();
                
                System.out.println("Testing ExpressionVisitor on: " + selectBody);
                
                TestExpressionVisitor visitor = new TestExpressionVisitor();
                System.out.println("Before accept - visited: " + visitor.wasVisited());
                
                // Call the accept method
                selectBody.getClass().getMethod("accept", net.sf.jsqlparser.expression.ExpressionVisitor.class)
                    .invoke(selectBody, visitor);
                
                System.out.println("After accept - visited: " + visitor.wasVisited());
                System.out.println("Visited function: " + visitor.getVisitedFunction());
            }
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
